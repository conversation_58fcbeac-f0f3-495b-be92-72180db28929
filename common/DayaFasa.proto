syntax = "proto3";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.dayafasa";
option java_outer_classname = "DayaFasaProto";
option objc_class_prefix = "DYF";

package id.co.plniconplus.legacy.dayafasa;

service DayaFasaService {
  rpc GetListDayaFasa (Empty) returns (DayaFasaResponseList) {}
}

message Empty {}

message DayaFasaResponse {
  int32 daya = 1;
  int32 fasa = 2;
}

message DayaFasaResponseList {
  repeated DayaFasaResponse dayaFasaList = 1;
}
