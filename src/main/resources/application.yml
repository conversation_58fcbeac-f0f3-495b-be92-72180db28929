"%test":
  quarkus:
    generate-code:
      grpc:
        scan-for-proto: "proto/*.proto"
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ***********************************
      username: system
      password: oracle
    hibernate-orm:
      log:
        sql: true
        bind-parameters: true
  grpc:
    timeout: 5 # in seconds
  daya:
    threshold: 22000
  default:
    fasa: 1

quarkus:
  grpc:
    clients:
      daya-fasa:
        host: localhost
        port: 9000
      hari-libur:
        host: localhost
        port: 9000
grpc:
  timeout: 5 # in seconds
daya:
  threshold: 22000
default:
  fasa: 1
