package id.co.plniconplus.util;

import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.jboss.resteasy.reactive.RestResponse;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/29/2025
 */
public class ResponseHandler {

    public static RestResponse<Object> generateResponse(Response response, Object entity) {
        HashMap<Object, Object> map = new HashMap<>();
        HashMap<Object, Object> metadata = new HashMap<>();
        metadata.put("message", response.getStatusInfo().getReasonPhrase());
        metadata.put("code", response.getStatus());
        if (entity != null)
            map.put("response", entity);
        map.put("metadata", metadata);

        return RestResponse.ResponseBuilder
                .create(response.getStatus())
                .entity(map)
                .type(MediaType.APPLICATION_JSON)
                .build();
    }
}
