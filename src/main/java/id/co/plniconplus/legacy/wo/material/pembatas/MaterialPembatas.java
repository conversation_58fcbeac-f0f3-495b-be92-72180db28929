package id.co.plniconplus.legacy.wo.material.pembatas;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/29/2025
 */
@Embeddable
public class MaterialPembatas extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KDARUS_PEMBATAS")
    public String kodeArusPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_PEMBATAS")
    public String kodePembPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_PEMBATAS")
    public String merekPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "JENIS_PEMBATAS")
    public String jenisPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "UKURAN_SETING_PEMBATAS")
    public String ukuranSettingPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "PHASA_PEMBATAS")
    public String fasaPembatas;

    @Size(max = 20)
    @Column(length = 20, name = "TEGANG_PEMBATAS")
    public String tegangPembatas;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_PEMBATAS")
    public String typePembatas;

    @Size(max = 24)
    @Column(length = 24, name = "NOPEMBATAS")
    public String noPembatas;

    @Size(max = 10)
    @Column(length = 20, name = "NOPABRIK_PEMBATAS")
    public String noPabrikPembatas;

    @Size(max = 24)
    @Column(length = 24, name = "NOREGISTER_PEMBATAS")
    public String noRegistrasiPembatas;

    @Column(name = "ARUS_PEMBATAS")
    public Long arusPembatas;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_PEMBATAS")
    public String tahunBuatPembatas;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_PEMBATAS")
    public String tahunTeraPembatas;

    @Column(name = "QTY_ARUSPEMBATAS")
    public Integer jumlahArusPembatas;
}
