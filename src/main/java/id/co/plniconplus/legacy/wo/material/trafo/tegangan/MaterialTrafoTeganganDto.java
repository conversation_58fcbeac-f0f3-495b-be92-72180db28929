package id.co.plniconplus.legacy.wo.material.trafo.tegangan;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@RegisterForReflection
public record MaterialTrafoTeganganDto(
        String kodeTrafoTegangan,
        String kodePembTrafoTegangan,
        String merekTrafoTegangan,
        String typeTrafoTegangan,
        String noTrafoTegangan,
        String noPabrikTrafoTegangan,
        String noRegisterTrafoTegangan,
        String tahunBuatTrafoTegangan,
        String tahunTeraTrafoTegangan,
        Integer jumlahTrafoTegangan) {

    public static MaterialTrafoTeganganDto fromEntity(MaterialTrafoTegangan materialTrafoTegangan) {
        return new MaterialTrafoTeganganDto(
                materialTrafoTegangan.kodeTrafoTegangan,
                materialTrafoTegangan.kodePembTrafoTegangan,
                materialTrafoTegangan.merekTrafoTegangan,
                materialTrafoTegangan.typeTrafoTegangan,
                materialTrafoTegangan.noTrafoTegangan,
                materialTrafoTegangan.noPabrikTrafoTegangan,
                materialTrafoTegangan.noRegisterTrafoTegangan,
                materialTrafoTegangan.tahunBuatTrafoTegangan,
                materialTrafoTegangan.tahunTeraTrafoTegangan,
                materialTrafoTegangan.jumlahTrafoTegangan);
    }

    public MaterialTrafoTegangan toEntity() {
        MaterialTrafoTegangan materialTrafoTegangan = new MaterialTrafoTegangan();
        materialTrafoTegangan.kodeTrafoTegangan = this.kodeTrafoTegangan;
        materialTrafoTegangan.kodePembTrafoTegangan = this.kodePembTrafoTegangan;
        materialTrafoTegangan.merekTrafoTegangan = this.merekTrafoTegangan;
        materialTrafoTegangan.typeTrafoTegangan = this.typeTrafoTegangan;
        materialTrafoTegangan.noTrafoTegangan = this.noTrafoTegangan;
        materialTrafoTegangan.noPabrikTrafoTegangan = this.noPabrikTrafoTegangan;
        materialTrafoTegangan.noRegisterTrafoTegangan = this.noRegisterTrafoTegangan;
        materialTrafoTegangan.tahunBuatTrafoTegangan = this.tahunBuatTrafoTegangan;
        materialTrafoTegangan.tahunTeraTrafoTegangan = this.tahunTeraTrafoTegangan;
        materialTrafoTegangan.jumlahTrafoTegangan = this.jumlahTrafoTegangan;
        return materialTrafoTegangan;
    }
}
