package id.co.plniconplus.legacy.wo.material.trafo.arus;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@RegisterForReflection
public record MaterialTrafoArusDto(
        String kodeTrafoArus,
        String kodePembTrafoArus,
        String merekTrafoArus,
        String typeTrafoArus,
        String noTrafoArus,
        String noPabrikTrafoArus,
        String noRegisterTrafoArus,
        String tahunBuatTrafoArus,
        String tahunTeraTrafoArus,
        Integer jumlahTrafoArus) {

    public static MaterialTrafoArusDto fromEntity(MaterialTrafoArus materialTrafoArus) {
        return new MaterialTrafoArusDto(
                materialTrafoArus.kodeTrafoArus,
                materialTrafoArus.kodePembTrafoArus,
                materialTrafoArus.merekTrafoArus,
                materialTrafoArus.typeTrafoArus,
                materialTrafoArus.noTrafoArus,
                materialTrafoArus.noPabrikTrafoArus,
                materialTrafoArus.noRegisterTrafoArus,
                materialTrafoArus.tahunBuatTrafoArus,
                materialTrafoArus.tahunTeraTrafoArus,
                materialTrafoArus.jumlahTrafoArus);
    }

    public MaterialTrafoArus toEntity() {
        MaterialTrafoArus materialTrafoArus = new MaterialTrafoArus();
        materialTrafoArus.kodeTrafoArus = this.kodeTrafoArus;
        materialTrafoArus.kodePembTrafoArus = this.kodePembTrafoArus;
        materialTrafoArus.merekTrafoArus = this.merekTrafoArus;
        materialTrafoArus.typeTrafoArus = this.typeTrafoArus;
        materialTrafoArus.noTrafoArus = this.noTrafoArus;
        materialTrafoArus.noPabrikTrafoArus = this.noPabrikTrafoArus;
        materialTrafoArus.noRegisterTrafoArus = this.noRegisterTrafoArus;
        materialTrafoArus.tahunBuatTrafoArus = this.tahunBuatTrafoArus;
        materialTrafoArus.tahunTeraTrafoArus = this.tahunTeraTrafoArus;
        materialTrafoArus.jumlahTrafoArus = this.jumlahTrafoArus;
        return materialTrafoArus;
    }
}
