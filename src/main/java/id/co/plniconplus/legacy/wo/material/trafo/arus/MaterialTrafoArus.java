package id.co.plniconplus.legacy.wo.material.trafo.arus;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Embeddable
public class MaterialTrafoArus extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_CT")
    public String kodeTrafoArus;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_CT")
    public String kodePembTrafoArus;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_CT")
    public String merekTrafoArus;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_CT")
    public String typeTrafoArus;

    @Size(max = 24)
    @Column(length = 24, name = "NO_CT")
    public String noTrafoArus;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_CT")
    public String noPabrikTrafoArus;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_CT")
    public String noRegisterTrafoArus;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_CT")
    public String tahunBuatTrafoArus;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_CT")
    public String tahunTeraTrafoArus;

    @Column(name = "QTY_CT")
    public Integer jumlahTrafoArus;
}
