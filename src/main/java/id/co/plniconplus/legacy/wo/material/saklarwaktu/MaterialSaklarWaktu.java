package id.co.plniconplus.legacy.wo.material.saklarwaktu;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Embeddable
public class MaterialSaklarWaktu extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_SAKLARWAKTU")
    public String kodeSaklarWaktu;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_SAKLARWAKTU")
    public String kodePembSaklarWaktu;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_SAKLARWAKTU")
    public String merekSaklarWaktu;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_SAKLARWAKTU")
    public String typeSaklarWaktu;

    @Size(max = 24)
    @Column(length = 24, name = "NOMOR_SAKLARWAKTU")
    public String noSaklarWaktu;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_SAKLARWAKTU")
    public String noPabrikSaklarWaktu;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_SAKLARWAKTU")
    public String noRegisterSaklarWaktu;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_SAKLARWAKTU")
    public String tahunBuatSaklarWaktu;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_SAKLARWAKTU")
    public String tahunTeraSaklarWaktu;

    @Size(max = 10)
    @Column(length = 10, name = "TEG_SAKLAR_WAKTU")
    public String teganganSaklarWaktu;

    @Size(max = 10)
    @Column(length = 10, name = "ARUS_SAKLAR_WAKTU")
    public String arusSaklarWaktu;

    @Size(max = 10)
    @Column(length = 10, name = "UKURAN_SETING_SAKLARWAKTU")
    public String ukuranSettingSaklarWaktu;

    @Size(max = 10)
    @Column(length = 10, name = "FASA_BATAS_SAKLARWAKTU")
    public String fasaBatasSaklarWaktu;

    @Column(name = "QTY_SAKLARWAKTU")
    public Integer jumlahSaklarWaktu;
}
