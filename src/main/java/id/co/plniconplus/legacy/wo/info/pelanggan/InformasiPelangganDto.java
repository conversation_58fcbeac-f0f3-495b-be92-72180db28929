package id.co.plniconplus.legacy.wo.info.pelanggan;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
@RegisterForReflection
public record InformasiPelangganDto(
        String idPelanggan,
        String namaPelanggan,
        String alamatPelanggan,
        String noTelpPelanggan,
        String noHpPelanggan) {

    public static InformasiPelangganDto fromEntity(InformasiPelanggan informasiPelanggan) {
        return new InformasiPelangganDto(
                informasiPelanggan.idPelanggan,
                informasiPelanggan.namaPelanggan,
                informasiPelanggan.alamatPelanggan,
                informasiPelanggan.noTelpPelanggan,
                informasiPelanggan.noHpPelanggan);
    }

    public InformasiPelanggan toEntity() {
        InformasiPelanggan informasiPelanggan = new InformasiPelanggan();
        informasiPelanggan.idPelanggan = this.idPelanggan;
        informasiPelanggan.namaPelanggan = this.namaPelanggan;
        informasiPelanggan.alamatPelanggan = this.alamatPelanggan;
        informasiPelanggan.noTelpPelanggan = this.noTelpPelanggan;
        informasiPelanggan.noHpPelanggan = this.noHpPelanggan;
        return informasiPelanggan;
    }
}
