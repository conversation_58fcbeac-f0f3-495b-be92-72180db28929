package id.co.plniconplus.legacy.wo;

import id.co.plniconplus.legacy.materialentry.MaterialEntryDto;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import jakarta.inject.Inject;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/29/2025
 */
@Path("/v1/work-orders")
public class WorkOrderResource {

    @Inject
    WorkOrderService woService;

    @GET
    @Path("/materials")
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get materials entries with pagination")
    @APIResponse(responseCode = "200", description = "List of materials entries")
    @APIResponse(responseCode = "400", description = "Invalid pagination parameters")
    public Multi<MaterialEntryDto> getMaterialEntries(
            @QueryParam("page") @DefaultValue("0") @Min(0) int page,
            @QueryParam("size") @DefaultValue("20") @Min(1) @Max(100) int size) {
        Log.debugv("Fetching material entries - page: {0}, size: {1}", page, size);

        try {
            return woService.getMaterialEntries(page, size);
        } catch (Exception e) {
            throw new WebApplicationException("Failed to fetch materials", Response.Status.INTERNAL_SERVER_ERROR);
        }
    }
}
