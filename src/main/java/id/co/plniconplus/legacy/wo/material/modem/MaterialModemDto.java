package id.co.plniconplus.legacy.wo.material.modem;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@RegisterForReflection
public record MaterialModemDto(
        String kodeModem,
        String kodePembModem,
        String merekModem,
        String typeModem,
        String noModem,
        String noPabrikModem,
        String noRegisterModem,
        String tahunBuatModem,
        String tahunTeraModem,
        String operatorSeluler,
        String speed,
        Integer jumlahModem) {

    public static MaterialModemDto fromEntity(MaterialModem materialModem) {
        return new MaterialModemDto(
                materialModem.kodeModem,
                materialModem.kodePembModem,
                materialModem.merekModem,
                materialModem.typeModem,
                materialModem.noModem,
                materialModem.noPabrikModem,
                materialModem.noRegisterModem,
                materialModem.tahunBuatModem,
                materialModem.tahunTeraModem,
                materialModem.operatorSeluler,
                materialModem.speed,
                materialModem.jumlahModem);
    }

    public MaterialModem toEntity() {
        MaterialModem materialModem = new MaterialModem();
        materialModem.kodeModem = this.kodeModem;
        materialModem.kodePembModem = this.kodePembModem;
        materialModem.merekModem = this.merekModem;
        materialModem.typeModem = this.typeModem;
        materialModem.noModem = this.noModem;
        materialModem.noPabrikModem = this.noPabrikModem;
        materialModem.noRegisterModem = this.noRegisterModem;
        materialModem.tahunBuatModem = this.tahunBuatModem;
        materialModem.tahunTeraModem = this.tahunTeraModem;
        materialModem.operatorSeluler = this.operatorSeluler;
        materialModem.speed = this.speed;
        materialModem.jumlahModem = this.jumlahModem;
        return materialModem;
    }
}
