package id.co.plniconplus.legacy.wo;

import id.co.plniconplus.legacy.wo.info.pelanggan.InformasiPelanggan;
import id.co.plniconplus.legacy.wo.info.pelapor.InformasiPelapor;
import id.co.plniconplus.legacy.wo.info.petugas.InformasiPetugas;
import id.co.plniconplus.legacy.wo.material.kabel.MaterialKabel;
import id.co.plniconplus.legacy.wo.material.kvamaks.MaterialKvamaks;
import id.co.plniconplus.legacy.wo.material.kvarh.MaterialKvarh;
import id.co.plniconplus.legacy.wo.material.kwh.MaterialKwh;
import id.co.plniconplus.legacy.wo.material.modem.MaterialModem;
import id.co.plniconplus.legacy.wo.material.pembatas.MaterialPembatas;
import id.co.plniconplus.legacy.wo.material.saklarwaktu.MaterialSaklarWaktu;
import id.co.plniconplus.legacy.wo.material.trafo.arus.MaterialTrafoArus;
import id.co.plniconplus.legacy.wo.material.trafo.tegangan.MaterialTrafoTegangan;
import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Entity
@Table(schema = "FSO", name = "TRANS_DATA_TEKNIK")
public class WorkOrder extends PanacheEntityBase {

    @EmbeddedId
    public WorkOrderId workOrderId;

    @Size(max = 30)
    @Column(length = 30, name = "ID_USER")
    public String userId;

    public InformasiPelanggan informasiPelanggan;

    public InformasiPelapor informasiPelapor;

    public InformasiPetugas informasiPetugas;

    public MaterialKabel materialKabel;

    public MaterialKvamaks materialKvamaks;

    public MaterialKvarh materialKvarh;

    public MaterialKwh materialKwh;

    public MaterialModem materialModem;

    public MaterialPembatas materialPembatas;

    public MaterialSaklarWaktu materialSaklarWaktu;

    public MaterialTrafoArus materialTrafoArus;

    public MaterialTrafoTegangan materialTrafoTegangan;

    @Column(name = "TGLAGENDA", columnDefinition = "DATE")
    public LocalDateTime tanggalAgenda;

    @Column(name = "TGL_ISI_MATERIAL", columnDefinition = "DATE")
    public LocalDateTime tanggalIsiMaterial;

    @Column(name = "TGL_LUNAS", columnDefinition = "DATE")
    public LocalDateTime tanggalLunas;

    @Column(name = "TGL_REMAJA", columnDefinition = "DATE")
    public LocalDateTime tanggalRemaja;

    @Column(name = "TGL_RESTITUSI", columnDefinition = "DATE")
    public LocalDateTime tanggalRestitusi;

    @NotEmpty
    @Size(max = 30)
    @Column(length = 30, name = "STATUS", nullable = false)
    public String status;

    @Size(max = 30)
    @Column(length = 30, name = "TARIF")
    public String tarif;

    @Size(max = 30)
    @Column(length = 30, name = "TARIF_LAMA")
    public String tarifLama;

    @Size(max = 30)
    @Column(length = 30, name = "DAYA")
    public String daya;

    @Size(max = 30)
    @Column(length = 30, name = "DAYA_LAMA")
    public String dayaLama;

    @Size(max = 30)
    @Column(length = 30, name = "JENIS_TRANSAKSI")
    public String jenisTransaksi;

    @Size(max = 10)
    @Column(length = 10, name = "KODE_PAKET")
    public String kodePaket;

    @Size(max = 20)
    @Column(length = 20, name = "NO_WO")
    public String noWorkOrder;

    @Column(name = "TGL_WO", columnDefinition = "DATE")
    public LocalDateTime tanggalWorkOrder;

    @Column(name = "TGLCATAT", columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;

    @Column(name = "STATUSGCM")
    public Integer messagingStatus;

    @Column(name = "HARI_SLA")
    public Integer hariSla;

    @Column(name = "TGLPK", columnDefinition = "DATE")
    public LocalDateTime tanggalPerintahKerja;

    @Column(name = "TGLPASANG_SAKLARWAKTU", columnDefinition = "DATE")
    public LocalDateTime tanggalPasangSaklarWaktu;
}
