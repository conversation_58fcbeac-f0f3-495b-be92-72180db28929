package id.co.plniconplus.legacy.wo.info.pelapor;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
@RegisterForReflection
public record InformasiPelaporDto(
        String namaPemohon,
        String alamatPemohon,
        String noTelpPemohon,
        String noHpPemohon) {

    public static InformasiPelaporDto fromEntity(InformasiPelapor informasiPelapor) {
        return new InformasiPelaporDto(
                informasiPelapor.namaPemohon,
                informasiPelapor.alamatPemohon,
                informasiPelapor.noTelpPemohon,
                informasiPelapor.noHpPemohon);
    }

    public InformasiPelapor toEntity() {
        InformasiPelapor informasiPelapor = new InformasiPelapor();
        informasiPelapor.namaPemohon = this.namaPemohon;
        informasiPelapor.alamatPemohon = this.alamatPemohon;
        informasiPelapor.noTelpPemohon = this.noTelpPemohon;
        informasiPelapor.noHpPemohon = this.noHpPemohon;
        return informasiPelapor;
    }
}
