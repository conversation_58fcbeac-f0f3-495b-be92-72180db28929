package id.co.plniconplus.legacy.wo.material.kabel;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Embeddable
public class MaterialKabel extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_KABEL")
    public String kodeKabel;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_KABEL")
    public String kodePembKabel;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_KABEL")
    public String merekKabel;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_KABEL")
    public String typeKabel;

    @Size(max = 24)
    @Column(length = 24, name = "NO_KABEL")
    public String noKabel;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_KABEL")
    public String noPabrikKabel;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_KABEL")
    public String noRegisterKabel;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_KABEL")
    public String tahunBuatKabel;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_KABEL")
    public String tahunTeraKabel;

    @Column(name = "QTY_KABEL")
    public Integer jumlahKabel;
}
