package id.co.plniconplus.legacy.wo.material.kvamaks;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@RegisterForReflection
public record MaterialKvamaksDto(
        String kodeKvamaks,
        String kodePembMeterKvamaks,
        String merekKvamaks,
        String typeKvamaks,
        String noMeterKvamaks,
        String noPabrikKvmaks,
        String noRegisterKvamaks,
        String tahunBuatKvamaks,
        String tahunTeraKvamaks,
        Integer jumlahKvamaks) {

    public static MaterialKvamaksDto fromEntity(MaterialKvamaks materialKvamaks) {
        return new MaterialKvamaksDto(
                materialKvamaks.kodeKvamaks,
                materialKvamaks.kodePembMeterKvamaks,
                materialKvamaks.merekKvamaks,
                materialKvamaks.typeKvamaks,
                materialKvamaks.noMeterKvamaks,
                materialKvamaks.noPabrikKvmaks,
                materialKvamaks.noRegisterKvamaks,
                materialKvamaks.tahunBuatKvamaks,
                materialKvamaks.tahunTeraKvamaks,
                materialKvamaks.jumlahKvamaks);
    }

    public MaterialKvamaks toEntity() {
        MaterialKvamaks materialKvamaks = new MaterialKvamaks();
        materialKvamaks.kodeKvamaks = this.kodeKvamaks;
        materialKvamaks.kodePembMeterKvamaks = this.kodePembMeterKvamaks;
        materialKvamaks.merekKvamaks = this.merekKvamaks;
        materialKvamaks.typeKvamaks = this.typeKvamaks;
        materialKvamaks.noMeterKvamaks = this.noMeterKvamaks;
        materialKvamaks.noPabrikKvmaks = this.noPabrikKvmaks;
        materialKvamaks.noRegisterKvamaks = this.noRegisterKvamaks;
        materialKvamaks.tahunBuatKvamaks = this.tahunBuatKvamaks;
        materialKvamaks.tahunTeraKvamaks = this.tahunTeraKvamaks;
        materialKvamaks.jumlahKvamaks = this.jumlahKvamaks;
        return materialKvamaks;
    }
}
