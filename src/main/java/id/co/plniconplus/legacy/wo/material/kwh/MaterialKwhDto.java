package id.co.plniconplus.legacy.wo.material.kwh;

import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@RegisterForReflection
public record MaterialKwhDto(
        String kodeKwh,
        String kodePembMeterKwh,
        String merekKwh,
        String typeKwh,
        String noMeterKwh,
        String noPabrikKwh,
        String noRegisterKwh,
        String tahunBuatKwh,
        String tahunTeraKwh,
        Integer jumlahKwh) {

    public static MaterialKwhDto fromEntity(MaterialKwh materialKwh) {
        return new MaterialKwhDto(
                materialKwh.kodeKwh,
                materialKwh.kodePembMeterKwh,
                materialKwh.merekKwh,
                materialKwh.typeKwh,
                materialKwh.noMeterKwh,
                materialKwh.noPabrikKwh,
                materialKwh.noRegisterKwh,
                materialKwh.tahunBuatKwh,
                materialKwh.tahunTeraKwh,
                materialKwh.jumlahKwh);
    }

    public MaterialKwh toEntity() {
        MaterialKwh materialKwh = new MaterialKwh();
        materialKwh.kodeKwh = this.kodeKwh;
        materialKwh.kodePembMeterKwh = this.kodePembMeterKwh;
        materialKwh.merekKwh = this.merekKwh;
        materialKwh.typeKwh = this.typeKwh;
        materialKwh.noMeterKwh = this.noMeterKwh;
        materialKwh.noPabrikKwh = this.noPabrikKwh;
        materialKwh.noRegisterKwh = this.noRegisterKwh;
        materialKwh.tahunBuatKwh = this.tahunBuatKwh;
        materialKwh.tahunTeraKwh = this.tahunTeraKwh;
        materialKwh.jumlahKwh = this.jumlahKwh;
        return materialKwh;
    }
}
