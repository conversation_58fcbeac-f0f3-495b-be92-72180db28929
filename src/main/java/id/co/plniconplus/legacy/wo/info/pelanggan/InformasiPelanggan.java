package id.co.plniconplus.legacy.wo.info.pelanggan;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Embeddable
public class InformasiPelanggan extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "IDPEL")
    public String idPelanggan;

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_PELANGGAN")
    public String namaPelanggan;

    @Size(max = 1000)
    @Column(length = 1000, name = "ALAMAT_PELANGGAN")
    public String alamatPelanggan;

    @Size(max = 30)
    @Column(length = 30, name = "NOTELP_PELANGGAN")
    public String noTelpPelanggan;

    @<PERSON>ze(max = 30)
    @Column(length = 30, name = "NOHP_PELANGGAN")
    public String noHpPelanggan;
}
